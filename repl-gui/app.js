class KashfREPL {
    constructor() {
        this.socket = null;
        this.editor = null;
        this.isConnected = false;
        this.executionHistory = [];
        
        this.init();
    }

    init() {
        this.initializeEditor();
        this.connectWebSocket();
        this.setupEventListeners();
    }

    initializeEditor() {
        this.editor = CodeMirror.fromTextArea(document.getElementById('code-editor'), {
            mode: 'javascript',
            theme: 'monokai',
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            indentUnit: 4,
            tabSize: 4,
            lineWrapping: true,
            extraKeys: {
                'Ctrl-Enter': () => this.executeCode(),
                'Ctrl-L': () => this.clearResults(),
                'Ctrl-K': () => this.clearEditor(),
                'Tab': 'autocomplete'
            },
            hintOptions: {
                completeSingle: false
            }
        });

        // Auto-resize editor
        this.editor.setSize(null, 200);
    }

    connectWebSocket() {
        // For now, use REST API instead of WebSocket
        this.checkConnection();
        this.getContextInfo();
    }

    async checkConnection() {
        try {
            const response = await fetch('/api/context');
            if (response.ok) {
                this.isConnected = true;
                this.updateConnectionStatus(true);
                console.log('Connected to Kashf REPL server');
            } else {
                throw new Error('Server not responding');
            }
        } catch (error) {
            this.isConnected = false;
            this.updateConnectionStatus(false);
            console.error('Connection error:', error);
        }
    }

    setupEventListeners() {
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.executeCode();
            }
        });

        // Button events
        document.getElementById('execute-btn').addEventListener('click', () => this.executeCode());
    }

    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-status');
        const statusText = document.getElementById('status-text');
        
        if (connected) {
            indicator.classList.add('connected');
            statusText.textContent = 'Connected';
        } else {
            indicator.classList.remove('connected');
            statusText.textContent = 'Disconnected';
        }
    }

    updateContextInfo(info) {
        const contextDiv = document.getElementById('context-info');
        
        if (info.error) {
            contextDiv.innerHTML = `<p style="color: #ff4757;">Error: ${info.error}</p>`;
            return;
        }

        let html = '';
        
        if (info.availableServices) {
            html += `<p><strong>Services:</strong> ${info.availableServices}</p>`;
        }
        
        if (info.contextKeys && info.contextKeys.length > 0) {
            html += `<p><strong>Available:</strong></p><ul>`;
            info.contextKeys.forEach(key => {
                html += `<li>${key}</li>`;
            });
            html += `</ul>`;
        }
        
        if (info.helpAvailable) {
            html += `<p><em>Type <code>help()</code> for more info</em></p>`;
        }
        
        contextDiv.innerHTML = html || '<p>No context information available</p>';
    }

    async executeCode() {
        if (!this.isConnected) {
            this.showResult({
                success: false,
                error: 'Not connected to REPL server'
            });
            return;
        }

        const code = this.editor.getValue().trim();
        if (!code) {
            this.showResult({
                success: false,
                error: 'No code to execute'
            });
            return;
        }

        this.setLoading(true);
        const startTime = Date.now();

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code }),
            });

            const result = await response.json();
            this.handleExecutionResult(result);
        } catch (error) {
            this.handleExecutionResult({
                success: false,
                error: 'Failed to execute code: ' + error.message
            });
        } finally {
            this.setLoading(false);
        }
    }

    handleExecutionResult(result) {
        const executionTime = Date.now() - this.lastExecutionStart;
        this.setLoading(false);
        this.showResult(result);
        this.updateExecutionTime(executionTime);
        
        // Add to history
        this.executionHistory.push({
            code: this.editor.getValue(),
            result: result,
            timestamp: new Date()
        });
    }

    showResult(result) {
        const resultsDiv = document.getElementById('results');
        
        let html = '';
        let className = '';
        
        if (result.success) {
            className = 'result-success';
            
            if (result.output) {
                html += `<div class="result-output"><strong>Output:</strong>\n${this.escapeHtml(result.output)}</div>\n`;
            }
            
            if (result.result !== undefined) {
                const resultStr = this.formatResult(result.result, result.type);
                html += `<div><strong>Result (${result.type}):</strong>\n${resultStr}</div>`;
            }
        } else {
            className = 'result-error';
            html += `<div><strong>Error:</strong>\n${this.escapeHtml(result.error)}</div>`;
            
            if (result.output) {
                html += `<div class="result-output"><strong>Output:</strong>\n${this.escapeHtml(result.output)}</div>`;
            }
        }
        
        resultsDiv.innerHTML = html;
        resultsDiv.className = `result-content ${className}`;
        
        // Scroll to bottom
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }

    formatResult(result, type) {
        if (result === null) return 'null';
        if (result === undefined) return 'undefined';
        
        if (type === 'object') {
            try {
                return JSON.stringify(result, null, 2);
            } catch (e) {
                return String(result);
            }
        }
        
        return this.escapeHtml(String(result));
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    setLoading(loading) {
        const loadingDiv = document.getElementById('loading');
        const executeBtn = document.getElementById('execute-btn');
        
        if (loading) {
            loadingDiv.classList.add('show');
            executeBtn.disabled = true;
            this.lastExecutionStart = Date.now();
        } else {
            loadingDiv.classList.remove('show');
            executeBtn.disabled = false;
        }
    }

    updateExecutionTime(time) {
        const timeDiv = document.getElementById('execution-time');
        timeDiv.textContent = `Executed in ${time}ms`;
    }

    clearResults() {
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = '<div style="opacity: 0.7;">Results cleared...</div>';
        resultsDiv.className = 'result-content';
        document.getElementById('execution-time').textContent = '';
    }

    clearEditor() {
        this.editor.setValue('');
        this.editor.focus();
    }

    async getContextInfo() {
        try {
            const response = await fetch('/api/context');
            if (response.ok) {
                const info = await response.json();
                this.updateContextInfo(info);
            }
        } catch (error) {
            console.error('Failed to get context info:', error);
        }
    }
}

// Example functions
function loadExample(type) {
    const repl = window.kashfREPL;
    
    const examples = {
        help: 'help()',
        basic: `// Basic JavaScript example
console.log('Hello from Kashf REPL!');
const message = 'This is working!';
console.log(message);
return message;`,
        async: `// Async/await example
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

console.log('Starting async operation...');
await delay(1000);
console.log('Async operation completed!');

return 'Async example finished';`,
        nestjs: `// NestJS and Database example
try {
    console.log('App context available:', !!app);

    if (app) {
        // Get configuration service
        const configService = getService('ConfigService');
        console.log('Config service loaded:', !!configService);

        // Try to get database connection
        try {
            const entityManager = getEntityManager();
            console.log('Database connection available:', !!entityManager);

            // Example query (adjust table name as needed)
            const result = await entityManager.query('SELECT 1 as test');
            console.log('Database test query result:', result);

            return 'NestJS and database context working!';
        } catch (dbError) {
            console.warn('Database not available:', dbError.message);
            return 'NestJS context available, but database connection failed';
        }
    } else {
        return 'NestJS context not available - running in basic mode';
    }
} catch (error) {
    console.error('Error:', error.message);
    return 'Error accessing NestJS context';
}`
    };
    
    if (examples[type]) {
        repl.editor.setValue(examples[type]);
        repl.editor.focus();
    }
}

// Global functions for buttons
function executeCode() {
    window.kashfREPL.executeCode();
}

function clearResults() {
    window.kashfREPL.clearResults();
}

function clearEditor() {
    window.kashfREPL.clearEditor();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.kashfREPL = new KashfREPL();
});
